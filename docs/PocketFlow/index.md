---
layout: default
title: "PocketFlow"
nav_order: 18
has_children: true
---

# Tutorial: PocketFlow

> This tutorial is AI-generated! To learn more, check out [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)

PocketFlow<sup>[View Repo](https://github.com/The-Pocket/PocketFlow)</sup> is a *Python framework* for building modular workflows and AI agents.
It allows you to define complex processes by connecting individual **Nodes**, which represent *atomic tasks* like calling an LLM or searching the web.
A **Flow** then *orchestrates* these Nodes, guiding the execution sequence based on **Actions** (string identifiers) returned by each Node.
Data is passed between Nodes and managed throughout the workflow execution via a **Shared State** (a Python dictionary).
PocketFlow also offers advanced features like **Batch Processing** for efficiently handling collections of items, and **Asynchronous Processing** for non-blocking operations crucial for I/O-bound tasks.
Additionally, it demonstrates an **A2A (Agent-to-Agent) Communication Framework** to wrap PocketFlow agents, enabling them to communicate with other systems using a standardized JSON-RPC protocol.

```mermaid
flowchart TD
    A0["Node (<code>BaseNode</code>, <code>Node</code>, <code>AsyncNode</code>)
"]
    A1["Flow (<code>Flow</code>, <code>AsyncFlow</code>)
"]
    A2["Shared State (<code>shared</code> dictionary)
"]
    A3["Actions / Transitions
"]
    A4["Batch Processing (<code>BatchNode</code>, <code>BatchFlow</code>, <code>AsyncParallelBatchNode</code>)
"]
    A5["Asynchronous Processing (<code>AsyncNode</code>, <code>AsyncFlow</code>)
"]
    A6["A2A (Agent-to-Agent) Communication Framework
"]
    A1 -- "Orchestrates Nodes" --> A0
    A0 -- "Accesses Shared State" --> A2
    A0 -- "Returns Action" --> A3
    A1 -- "Uses Action for dispatch" --> A3
    A4 -- "Specializes Node (batch)" --> A0
    A4 -- "Specializes Flow (batch)" --> A1
    A5 -- "Specializes Node (async)" --> A0
    A5 -- "Specializes Flow (async)" --> A1
    A6 -- "Executes Flow" --> A1
    A6 -- "Initializes Shared State" --> A2
```

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)